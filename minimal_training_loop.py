import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
import numpy as np

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

class LinearModel(nn.Module):
    """Simple linear model: y = Wx + b"""
    def __init__(self, input_size, output_size):
        super(LinearModel, self).__init__()
        self.linear = nn.Linear(input_size, output_size)
    
    def forward(self, x):
        return self.linear(x)

def generate_random_data(num_samples=1000, input_size=10, output_size=1):
    """Generate random input-output pairs"""
    X = torch.randn(num_samples, input_size)
    # Create a simple linear relationship with some noise
    true_weights = torch.randn(input_size, output_size)
    true_bias = torch.randn(output_size)
    y = X @ true_weights + true_bias + 0.1 * torch.randn(num_samples, output_size)
    return X, y, true_weights, true_bias

def train_model(model, X, y, num_epochs=100, learning_rate=0.01):
    """Training loop"""
    criterion = nn.MSELoss()
    optimizer = optim.SGD(model.parameters(), lr=learning_rate)
    
    losses = []
    
    print(f"Starting training for {num_epochs} epochs...")
    print(f"Learning rate: {learning_rate}")
    print("-" * 50)
    
    for epoch in range(num_epochs):
        # Forward pass
        predictions = model(X)
        loss = criterion(predictions, y)
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        # Store loss for plotting
        losses.append(loss.item())
        
        # Print progress every 10 epochs
        if (epoch + 1) % 10 == 0:
            print(f"Epoch [{epoch+1}/{num_epochs}], Loss: {loss.item():.6f}")
    
    print("-" * 50)
    print(f"Training completed! Final loss: {losses[-1]:.6f}")
    return losses

def plot_training_loss(losses):
    """Plot the training loss over epochs"""
    plt.figure(figsize=(10, 6))
    plt.plot(losses)
    plt.title('Training Loss Over Time')
    plt.xlabel('Epoch')
    plt.ylabel('Loss (MSE)')
    plt.grid(True)
    plt.show()

def compare_weights(model, true_weights, true_bias):
    """Compare learned weights with true weights"""
    learned_weights = model.linear.weight.data
    learned_bias = model.linear.bias.data
    
    print("\nWeight Comparison:")
    print(f"True weights shape: {true_weights.shape}")
    print(f"Learned weights shape: {learned_weights.shape}")
    print(f"Weight difference (L2 norm): {torch.norm(learned_weights.T - true_weights).item():.6f}")
    
    print(f"\nBias Comparison:")
    print(f"True bias: {true_bias.numpy()}")
    print(f"Learned bias: {learned_bias.numpy()}")
    print(f"Bias difference (L2 norm): {torch.norm(learned_bias - true_bias).item():.6f}")

def main():
    # Hyperparameters
    input_size = 10
    output_size = 1
    num_samples = 1000
    num_epochs = 100
    learning_rate = 0.01
    
    print("=== Minimal PyTorch Training Loop ===")
    print(f"Input size: {input_size}")
    print(f"Output size: {output_size}")
    print(f"Number of samples: {num_samples}")
    print(f"Number of epochs: {num_epochs}")
    print()
    
    # Generate random data
    print("Generating random data...")
    X, y, true_weights, true_bias = generate_random_data(num_samples, input_size, output_size)
    print(f"Data shapes - X: {X.shape}, y: {y.shape}")
    print()
    
    # Create model
    print("Creating linear model...")
    model = LinearModel(input_size, output_size)
    print(f"Model: {model}")
    print(f"Number of parameters: {sum(p.numel() for p in model.parameters())}")
    print()
    
    # Train the model
    losses = train_model(model, X, y, num_epochs, learning_rate)
    
    # Compare learned weights with true weights
    compare_weights(model, true_weights, true_bias)
    
    # Plot training loss
    print("\nPlotting training loss...")
    plot_training_loss(losses)
    
    # Test the model with new data
    print("\nTesting with new data...")
    X_test = torch.randn(5, input_size)
    y_test_pred = model(X_test)
    print(f"Test input shape: {X_test.shape}")
    print(f"Test predictions shape: {y_test_pred.shape}")
    print(f"Sample predictions: {y_test_pred.squeeze().detach().numpy()}")

if __name__ == "__main__":
    main()
