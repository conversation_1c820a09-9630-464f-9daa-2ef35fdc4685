#!/bin/bash
# Convenience script to run Python files with the correct Python version

PYTHON_PATH="/opt/homebrew/bin/python3.11"

if [ $# -eq 0 ]; then
    echo "Usage: ./run.sh <python_file>"
    echo "Available files:"
    echo "  minimal_training_loop.py - Main training loop"
    echo "  test_config.py - Test configuration object"
    echo "  test_training_loop.py - Full test suite"
    echo "  start.py - Entry point"
    exit 1
fi

echo "Running $1 with Python 3.11..."
echo "Command: $PYTHON_PATH $1"
echo "----------------------------------------"

$PYTHON_PATH "$@"
