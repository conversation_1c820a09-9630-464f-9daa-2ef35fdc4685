#!/usr/bin/env python3
"""
Entry point for the minimal training loop project
"""

if __name__ == "__main__":
    print("=== Little Training Loop ===")
    print()
    print("Available scripts:")
    print("1. minimal_training_loop.py - Full training loop with detailed output")
    print("2. test_simple.py - Simple test to verify PyTorch installation")
    print("3. test_training_loop.py - Unit tests for the training loop components")
    print()
    print("To run the main training loop:")
    print("  python minimal_training_loop.py")
    print()
    print("To run tests:")
    print("  python test_training_loop.py")

    # Import and run the main training loop
    from minimal_training_loop import run

    print("\nRunning minimal training loop...")
    print("=" * 50)
    run()
