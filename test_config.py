#!/usr/bin/env python3
"""
Quick test of the TrainingConfig object
"""

print("Testing TrainingConfig...")

try:
    from minimal_training_loop import TrainingConfig
    
    # Test default config
    config = TrainingConfig()
    print(f"✓ Default config created:")
    print(f"  Input size: {config.input_size}")
    print(f"  Output size: {config.output_size}")
    print(f"  Num samples: {config.num_samples}")
    print(f"  Num epochs: {config.num_epochs}")
    print(f"  Learning rate: {config.learning_rate}")
    print(f"  Random seed: {config.random_seed}")
    print(f"  Print every: {config.print_every}")
    
    # Test custom config
    custom_config = TrainingConfig(
        input_size=5,
        output_size=2,
        num_epochs=50,
        learning_rate=0.001
    )
    print(f"\n✓ Custom config created:")
    print(f"  Input size: {custom_config.input_size}")
    print(f"  Output size: {custom_config.output_size}")
    print(f"  Num epochs: {custom_config.num_epochs}")
    print(f"  Learning rate: {custom_config.learning_rate}")
    
    print("\n✓ TrainingConfig working correctly!")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
